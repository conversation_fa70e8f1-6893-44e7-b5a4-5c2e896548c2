<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Models\AwwardPackage;
use App\Models\FestivalPackage;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\ConferencePackage;
use App\Service\TransactionService;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Http\Requests\TransactionRequest;
use Illuminate\Support\Facades\Mail;
use App\Mail\InvoiceEmail;

require_once base_path('vendor/stripe/stripe-php/init.php');

class TransactionController extends Controller
{
    private $transactionService;
    
    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
        $this->middleware('auth');
    }

    public function store(TransactionRequest $request)
    {
        $user = auth()->user();
        $validated = $request->validated();
        $cart = $this->transactionService->getCart($user);
        
        Log::info('Cart items', [
            'user_id' => $user->id,
            'cart_items' => $cart->toArray(),
        ]);

        if ($cart->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Cart is empty or contains invalid items.',
            ], 400);
        }

        try {
            $transaction = null;

            if ($request->payment_method === 'invoice') {
                Log::info('Attempting to generate invoice...');
                Log::info('Request data:', $request->all());
                Log::info('Cart data:', $cart->toArray());
                try {
                    $invoiceNumber = 'INV-' . time();
                    $paymentDeadline = now()->addDays(7)->format('Y-m-d');

                    $subtotalPrice = $cart->sum('price');
                    $vatRate = 0.20;
                    $vatPrice = $subtotalPrice * $vatRate;
                    $priceExcludingVat = $subtotalPrice - $vatPrice;

                    // PDF generation - გააქტიურება
                    Log::info('Activating PDF generation...');
                    
                    $pdf = Pdf::loadView('emails.invoice-template', [
                        'customerName' => $user->name,
                        'invoiceNumber' => $invoiceNumber,
                        'paymentDeadline' => $paymentDeadline,
                        'name_surname' => $request->name,
                        'job_title' => $request->job,
                        'company' => $request->company,
                        'address' => $request->address1 . ($request->address2 ? ', ' . $request->address2 : '') . ', ' . $request->city . ', ' . $request->zip_code . ', ' . $request->country,
                        'email' => $request->email,
                        'phone' => $request->mobile,
                        'subtotal_price' => $subtotalPrice,
                        'vat_price' => $vatPrice,
                        'cart' => $cart, // Cart items დამატება
                    ]);
                    
                    Log::info('PDF view loaded successfully');

                    // Email directory შექმნა
                    $emailDir = storage_path('app/email');
                    if (!file_exists($emailDir)) {
                        mkdir($emailDir, 0755, true);
                        Log::info('Created email directory: ' . $emailDir);
                    }

                    $pdfFileName = 'invoice-' . $invoiceNumber . '.pdf';
                    $pdfPath = 'email/' . $pdfFileName;
                    $fullPdfPath = storage_path('app/' . $pdfPath);
                    
                    // PDF შენახვა აქტიურდება
                    $pdf->save($fullPdfPath);
                    
                    // File არსებობის შემოწმება
                    if (file_exists($fullPdfPath)) {
                        Log::info('SUCCESS: PDF saved at: ' . $fullPdfPath);
                    } else {
                        Log::error('FAILED: PDF was not saved');
                        throw new \Exception('PDF file was not created successfully');
                    }
                    
                    // Email გაგზავნა - ეს ბლოკი ახლა მთლიანად კომენტარებშია მოქცეული
                    /*
                    try {
                        Log::info("Attempting to send invoice email to: {$request->email}");
                        
                        Mail::to($request->email)->send(new InvoiceEmail(
                            $fullPdfPath,
                            $invoiceNumber,
                            $paymentDeadline,
                            $request->name
                        ));

                        Log::info('Invoice email sent successfully to: ' . $request->email);
                    } catch (\Exception $e) {
                        Log::error('Failed to send invoice email: ' . $e->getMessage());
                    }
                    */
                    Log::info('Email sending skipped as per request.'); // დაამატეთ ეს ლოგი, რომ იცოდეთ, რომ გამოტოვებულია


                    // Transaction შექმნა
                    $transaction = $user->transactions()->create([
                        'name' => $request->name,
                        'email' => $request->email,
                        'mobile' => $request->mobile,
                        'company' => $request->company,
                        'job' => $request->job,
                        'address1' => $request->address1,
                        'address2' => $request->address2,
                        'city' => $request->city,
                        'zip_code' => $request->zip_code,
                        'country' => $request->country,
                        'payment_method' => 'invoice',
                        'price' => $subtotalPrice,
                        'success' => false,
                        'file_path' => $pdfPath,
                        'file_name' => $pdfFileName,
                    ]);

                    // Cart items დამატება
                    foreach ($cart as $item) {
                        // შეამოწმეთ, არის თუ არა $item->itemable ობიექტი და არა null
                        if ($item->itemable) {
                            $transaction->items()->create([
                                'model_type' => get_class($item->itemable),
                                'model_id' => $item->itemable->id,
                                'price' => $item->price,
                                'user_id' => $user->id
                            ]);
                        } else {
                            Log::warning('Cart item has null itemable property', [
                                'cart_item_id' => $item->id ?? 'N/A',
                                'user_id' => $user->id,
                            ]);
                        }
                    }

                    Log::info('Transaction created successfully with ID: ' . $transaction->id);

                    // გადამისამართება success გვერდზე ინვოისის გვერდის ნაცვლად
                    return response()->json([
                        'success' => true,
                        'message' => 'Invoice generated successfully and payment process completed.',
                        'redirect_url' => route('transaction.success', $transaction), // <--- აქ შევცვალეთ
                    ]);

                } catch (\Exception $e) {
                    Log::error('PDF generation or invoice transaction creation failed: ' . $e->getMessage());
                    Log::error('Full error trace: ' . $e->getTraceAsString());
                    Log::error('Error at line: ' . $e->getLine() . ' in file: ' . $e->getFile());
                    return response()->json([
                        'success' => false,
                        'message' => 'Invoice generation failed: ' . $e->getMessage(),
                    ], 500);
                }
            } else {
                // Stripe payment
                Log::info('Attempting to handle Stripe payment...');
                $transaction = $this->transactionService->handleStripePayment($user, $request, $cart);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment successful!',
                    'redirect_url' => route('transaction.success', $transaction),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Transaction failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'request_data' => $request->all(),
                'cart' => $cart->toArray(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Transaction failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    // ... დანარჩენი მეთოდები (success, invoice, conference, festival, awward) უცვლელია
    public function success(Request $request, Transaction $transaction)
    {
        return view('payment.success', compact('transaction'));
    }

    public function invoice(Request $request, Transaction $transaction)
    {
        $path = storage_path('app/' . $transaction->file_path);

        if (!file_exists($path)) {
            abort(404, 'Invoice file not found.');
        }

        return response()->download($path, $transaction->file_name ?? basename($path));
    }

    public function conference(Request $request, ConferencePackage $package)
    {
        $user = auth()->user();

        try {
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

            $priceInPence = 0;
            if (optional($package->conference)->location_id == 1) {
                $priceInPence = ($package->price * 1.2) * 100;
            } else {
                $priceInPence = $package->price * 100;
            }

            $charge = \Stripe\Charge::create([
                "amount" => $priceInPence,
                "currency" => "gbp",
                "source" => $request->stripeToken,
                "description" => "Conference Package: " . $package->title,
                "receipt_email" => $request->email,
            ]);

            $finalPrice = $priceInPence / 100;

            $transaction = $user->transactions()->create([
                'name' => $request->name,
                'email' => $request->email,
                'mobile' => $request->mobile,
                'company' => $request->company,
                'job' => $request->job,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'zip_code' => $request->zip_code,
                'country' => $request->country,
                'payment_method' => 'card',
                'price' => $finalPrice,
                'success' => true,
                'stripe_id' => $charge->id,
            ]);

            $transaction->items()->create([
                'model_type' => ConferencePackage::class,
                'model_id' => $package->id,
                'price' => $finalPrice,
                'user_id' => $user->id
            ]);

            return redirect()->route('transaction.success', $transaction);

        } catch (\Exception $e) {
            \Log::error('Stripe Payment failed for conference', ['error' => $e->getMessage()]);
            return back()->with('error', 'Payment failed: ' . $e->getMessage());
        }
    }

    public function festival(Request $request, FestivalPackage $package)
    {
        $user = auth()->user();

        try {
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

            $priceInPence = 0;
            if(optional($package->festival)->location_id == 1){
                $priceInPence = ($package->price * 1.2) * 100;
            } else {
                $priceInPence = $package->price * 100;
            }

            $charge = \Stripe\Charge::create([
                "amount" => $priceInPence,
                "currency" => "gbp",
                "source" => $request->stripeToken,
                "description" => "Festival Package: " . $package->title,
                "receipt_email" => $request->email,
            ]);

            $transaction = $user->transactions()->create([
                'name' => $request->name,
                'email' => $request->email,
                'mobile' => $request->mobile,
                'company' => $request->company,
                'job' => $request->job,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'zip_code' => $request->zip_code,
                'country' => $request->country,
                'payment_method' => 'card',
                'price' => $package->price,
                'success' => true,
                'stripe_id' => $charge->id,
            ]);

            $transaction->items()->create([
                'model_type' => FestivalPackage::class,
                'model_id' => $package->id,
                'price' => $package->price,
                'user_id' => $user->id
            ]);

            return redirect()->route('transaction.success', $transaction);

        } catch (\Exception $e) {
            Log::error('Stripe Payment failed for festival', ['error' => $e->getMessage()]);
            return back()->with('error', 'Payment failed: ' . $e->getMessage());
        }
    }

    public function awward(Request $request, AwwardPackage $package)
    {
        $user = auth()->user();

        $transaction = $user->transactions()->create(array_merge($request->all(), ['price' => 0]));

        $transaction->items()->create([
            'model_type' => AwwardPackage::class,
            'model_id' => $package->id,
            'price' => $package->price,
            'user_id' => $user->id
        ]);

        $transaction->price = $package->price;
        $transaction->success = true;
        $transaction->save();

        return redirect()->route('profile');
    }
}