<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogController extends Controller
{
    /**
     * Display a listing of the blogs.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $blogs = Blog::latest()->paginate(9);
        return view('blog.index', compact('blogs'));
    }

    /**
     * Display the specified blog.
     *
     * @param  string  $slug
     * @return \Illuminate\Http\Response
     */
    public function show($slug)
    {
        \Log::info('Blog show method called with slug: ' . $slug);
        
        // Remove any ":1" suffix that might be in the slug
        $cleanSlug = preg_replace('/:1$/', '', $slug);
        
        // Check if the parameter is numeric (ID) or a string (slug)
        if (is_numeric($cleanSlug)) {
            $post = Blog::findOrFail($cleanSlug);
        } else {
            // Try to find with the cleaned slug
            $post = Blog::where('slug', $cleanSlug)->first();
            
            // If not found, try with URL decoded slug
            if (!$post) {
                $decodedSlug = urldecode($cleanSlug);
                $post = Blog::where('slug', $decodedSlug)->first();
            }
            
            // If still not found, try with the ID
            if (!$post && is_numeric($slug)) {
                $post = Blog::findOrFail($slug);
            }
            
            // If still not found, throw 404
            if (!$post) {
                abort(404, "Blog post not found with slug: {$slug}");
            }
        }
        
        return view('blog.show', compact('post'));
    }
}
