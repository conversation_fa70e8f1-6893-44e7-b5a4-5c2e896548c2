<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Transaction;

class ProfileController extends Controller
{
    public function index($tab = 'me')
    {
        $user = Auth::user();
        
        // Get user's transactions
        $transactions = Transaction::where('user_id', $user->id)
            ->with('items')
            ->orderBy('created_at', 'desc')
            ->get();
        
        return view('profile.index', compact('user', 'transactions', 'tab'));
    }
    
    public function update(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
        ]);
        
        $user->first_name = $request->first_name;
        $user->last_name = $request->last_name;
        $user->email = $request->email;
        
        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }
        
        $user->save();
        
        return redirect()->route('profile')->with('success', 'Profile updated successfully!');
    }
    
    public function logout()
    {
        Auth::logout();
        return redirect()->route('login');
    }
}
