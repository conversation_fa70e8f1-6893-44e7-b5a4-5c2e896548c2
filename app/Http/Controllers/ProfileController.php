<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CourseLocation;
use App\Models\TransactionItem;
use App\Models\ConferencePackage;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\UpdateProfileRequest;

class ProfileController extends Controller
{

    const TABS = ['me', 'edit', 'history'];

    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Request $request, $tab = null)
    {
        $tab = $tab ?? self::TABS[0];

        abort_if(!in_array($tab, self::TABS), 404);

        $data = [];

        if(method_exists($this, $tab)){
            $data = $this->{$tab}();
        }

        return view('auth.profile', array_merge(['tab' => $tab, 'user' => auth()->user()], $data));
    }

    public function update(UpdateProfileRequest $request)
    {
        $user = auth()->user();

        $data = $request->except('password');

        if ($request->hasFile('image')) {
            $data['image'] = $request->image->store('public/upload/avatars');
        }

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return back()->withErrors(['success' => 'success']);
    }

    public function me(){
        return [
            'myConference' => TransactionItem::query()
                ->where('model_type', ConferencePackage::class)
                ->where('user_id', auth()->id())
                ->whereHas('model')
                ->with('model')
                ->get(),
            'myCourse' => TransactionItem::query()
                ->where('model_type', CourseLocation::class)
                ->where('user_id', auth()->id())
                ->with('model')
                ->get(),
            'myFestival' => TransactionItem::query()
                ->where('model_type', 'App\Models\FestivalPackage')
                ->where('user_id', auth()->id())
                ->with('model')
                ->get(),
            'myAwward' => TransactionItem::query()
                ->where('model_type', 'App\Models\AwwardPackage')
                ->where('user_id', auth()->id())
                ->with('model')
                ->get(),
        ];
    }
    public function logout()
    {
        Auth::logout();

        return redirect()->route('login');
    }

}
