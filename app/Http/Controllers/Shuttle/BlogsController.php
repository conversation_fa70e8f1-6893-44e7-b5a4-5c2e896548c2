<?php

namespace App\Http\Controllers\Shuttle;

use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Models\ScaffoldInterface;
use Illuminate\Http\Request;
use App\Models\Blog;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BlogsController extends ShuttleController
{
    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        // Get search parameters from form
        $searchQuery = $request->get('q');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // Start with the base query
        $query = Blog::query();

        // Apply search if provided
        if (!empty($searchQuery)) {
            $query->where(function($q) use ($searchQuery) {
                $q->where('title', 'LIKE', "%{$searchQuery}%")
                  ->orWhere('keyword', 'LIKE', "%{$searchQuery}%")
                  ->orWhere('description', 'LIKE', "%{$searchQuery}%")
                  ->orWhere('text', 'LIKE', "%{$searchQuery}%");
            });
        }

        // Apply date filters
        if (!empty($startDate)) {
            $query->whereDate('created_at', '>=', $startDate);
        }

        if (!empty($endDate)) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        // Get total count before pagination
        $totalRecords = Blog::count();
        $filteredRecords = $query->count();

        // Apply ordering
        $orderColumn = $request->get('order')[0]['column'] ?? 0;
        $orderDirection = $request->get('order')[0]['dir'] ?? 'desc';

        $columns = ['id', 'title', 'keyword', 'description', 'image', 'created_at'];
        $orderBy = $columns[$orderColumn] ?? 'created_at';

        $query->orderBy($orderBy, $orderDirection);

        // Apply pagination
        $start = $request->get('start', 0);
        $length = $request->get('length', 10);

        $blogs = $query->skip($start)->take($length)->get();

        // Format data for DataTables
        $data = [];
        foreach ($blogs as $blog) {
            $imageHtml = '';
            if ($blog->image) {
                $imageUrl = Storage::url($blog->image);
                $imageHtml = '<img src="' . $imageUrl . '" width="60" height="60" style="object-fit: cover; border-radius: 4px;">';
            }

            $data[] = [
                'id' => $blog->id,
                'title' => Str::limit($blog->title, 50),
                'keyword' => Str::limit($blog->keyword, 30),
                'description' => Str::limit($blog->description, 60),
                'image' => $imageHtml,
                'created_at' => $blog->created_at->format('Y-m-d H:i:s'),
                'actions' => $this->generateActionButtons($blog, $scaffoldInterface)
            ];
        }

        return response()->json([
            'draw' => intval($request->get('draw')),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }
    
    private function generateActionButtons($blog, $scaffoldInterface)
    {
        $buttons = '';
        
        // View button
        $buttons .= '<a href="' . route('shuttle.scaffold_interface.show', ['scaffold_interface' => $scaffoldInterface, 'id' => $blog->id]) . '" class="btn btn-sm btn-info">View</a> ';
        
        // Edit button
        $buttons .= '<a href="' . route('shuttle.scaffold_interface.edit', ['scaffold_interface' => $scaffoldInterface, 'id' => $blog->id]) . '" class="btn btn-sm btn-primary">Edit</a> ';
        
        // Delete button
        $buttons .= '<button type="button" class="btn btn-sm btn-danger remove-item" data-id="' . $blog->id . '">Delete</button>';
        
        return $buttons;
    }
}
