<?php

namespace App\Http\Controllers\Shuttle;

use Sina\Shuttle\Http\Controllers\ShuttleController;
use Sina\Shuttle\Models\ScaffoldInterface;
use Illuminate\Http\Request;
use App\Models\Blog;

class BlogsController extends ShuttleController
{
    public function datatable(Request $request, ScaffoldInterface $scaffoldInterface)
    {
        // Get search parameters
        $search = $request->get('search');
        $searchValue = $search['value'] ?? '';
        
        // Start with the base query
        $query = Blog::query();
        
        // Apply search if provided
        if (!empty($searchValue)) {
            $query->where(function($q) use ($searchValue) {
                $q->where('title', 'LIKE', "%{$searchValue}%")
                  ->orWhere('keyword', 'LIKE', "%{$searchValue}%")
                  ->orWhere('description', 'LIKE', "%{$searchValue}%")
                  ->orWhere('text', 'LIKE', "%{$searchValue}%");
            });
        }
        
        // Get total count before pagination
        $totalRecords = Blog::count();
        $filteredRecords = $query->count();
        
        // Apply ordering
        $orderColumn = $request->get('order')[0]['column'] ?? 0;
        $orderDirection = $request->get('order')[0]['dir'] ?? 'desc';
        
        $columns = ['id', 'title', 'keyword', 'description', 'image', 'created_at', 'updated_at'];
        $orderBy = $columns[$orderColumn] ?? 'created_at';
        
        $query->orderBy($orderBy, $orderDirection);
        
        // Apply pagination
        $start = $request->get('start', 0);
        $length = $request->get('length', 10);
        
        $blogs = $query->skip($start)->take($length)->get();
        
        // Format data for DataTables
        $data = [];
        foreach ($blogs as $blog) {
            $data[] = [
                'id' => $blog->id,
                'title' => $blog->title,
                'keyword' => $blog->keyword,
                'description' => $blog->description,
                'image' => $blog->image ? '<img src="' . asset('storage/' . $blog->image) . '" width="50" height="50" style="object-fit: cover;">' : '',
                'created_at' => $blog->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $blog->updated_at->format('Y-m-d H:i:s'),
                'actions' => $this->generateActionButtons($blog, $scaffoldInterface)
            ];
        }
        
        return response()->json([
            'draw' => intval($request->get('draw')),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }
    
    private function generateActionButtons($blog, $scaffoldInterface)
    {
        $buttons = '';
        
        // View button
        $buttons .= '<a href="' . route('shuttle.scaffold_interface.show', ['scaffold_interface' => $scaffoldInterface, 'id' => $blog->id]) . '" class="btn btn-sm btn-info">View</a> ';
        
        // Edit button
        $buttons .= '<a href="' . route('shuttle.scaffold_interface.edit', ['scaffold_interface' => $scaffoldInterface, 'id' => $blog->id]) . '" class="btn btn-sm btn-primary">Edit</a> ';
        
        // Delete button
        $buttons .= '<button type="button" class="btn btn-sm btn-danger remove-item" data-id="' . $blog->id . '">Delete</button>';
        
        return $buttons;
    }
}
