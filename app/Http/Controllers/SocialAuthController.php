<?php

namespace App\Http\Controllers;

use Socialite;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class SocialAuthController extends Controller
{
    public function redirectToGoogle()
    {
        try {
            Log::info('Google OAuth redirect initiated');
            Log::info('Google Client ID: ' . config('services.google.client_id'));
            Log::info('Google Redirect URI: ' . config('services.google.redirect'));

            return Socialite::driver('google')
                ->scopes(['openid', 'profile', 'email'])
                ->redirect();
        } catch (\Exception $e) {
            Log::error('Google OAuth redirect error: ' . $e->getMessage());
            return redirect('/login')->withErrors('Google login setup error: ' . $e->getMessage());
        }
    }

    public function handleGoogleCallback()
    {
        try {
        Log::info('Session ID before callback: ' . session()->getId());
        $googleUser = Socialite::driver('google')->user();
        Log::info('Google User: ' . json_encode($googleUser));
        Log::info('Session ID after callback: ' . session()->getId());
    } catch (\Exception $e) {
        Log::error('Google Auth Error: ' . $e->getMessage());
        return redirect('/login')->withErrors('Google login failed: ' . $e->getMessage());
    }
        // dd($googleUser);
        // Check if user already exists
        $user = User::where('email', $googleUser->getEmail())->first();

        if (!$user) {
            // Register user
            $user = User::create([
                'first_name' => $googleUser->user['given_name'] ?? '',
                'last_name' => $googleUser->user['family_name'] ?? '',
                'email' => $googleUser->getEmail(),
                'email_verified_at' => now(),
                'image' => $googleUser->getAvatar(), // profile picture
                'password' => bcrypt(Str::random(16)), // random password
                'google_id' => $googleUser->getId(),
                // 'mobile'=>Str::random(11),
            ]);
        }

        Auth::login($user);

        return redirect('/profile');
    }
    public function redirectToLinkedIn()
    {
        return Socialite::driver('linkedin-openid')
            ->scopes(['openid', 'profile', 'email'])
            ->redirect();
    }

    public function handleLinkedInCallback()
    {
        try {
            try {
                $linkedInUser = Socialite::driver('linkedin-openid')->user();
                // If you encounter CSRF issues in local development, consider:
                // $linkedInUser = Socialite::driver('linkedin-openid')->stateless()->user();
            } catch (\Exception $e) {
                Log::error('LinkedIn Socialite Driver Error', [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'state' => request()->get('state'),
                    'query' => request()->all(),
                ]);
                return redirect('/login')->withErrors('LinkedIn login failed due to an authentication error. Please try again.');
            }

            // Ensure email is present, as it's often a key identifier
            if (empty($linkedInUser->getEmail())) {
                Log::warning('LinkedIn user did not provide an email address.');
                return redirect('/login')->withErrors('LinkedIn login failed: We could not retrieve your primary email address. Please ensure it is public on LinkedIn.');
            }
            // Attempt to find user by email first (primary identifier)
            $user = User::where('email', $linkedInUser->getEmail())->first();

            if (!$user) {
                // If user doesn't exist, create a new one
                $fullName = $linkedInUser->getName();
                $firstName = null;
                $lastName = null;

                // Attempt to split the full name into first and last
                if ($fullName) {
                    $nameParts = explode(' ', $fullName, 2); // Split into max 2 parts
                    $firstName = $nameParts[0] ?? '';
                    $lastName = $nameParts[1] ?? '';
                }

                $user = User::create([
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'email' => $linkedInUser->getEmail(),
                    'email_verified_at' => now(),
                    'image' => $linkedInUser->getAvatar(),
                    'password' => bcrypt(Str::random(16)), // Generate a random password
                    // 'mobile' => Str::random(11), // Consider making this nullable or fetching it if possible
                ]);
            }

            auth()->login($user);

            return redirect('/profile');

        } catch (\Exception $e) {
            Log::error('Unhandled LinkedIn Callback Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'code' => request('code'),
                'state' => request('state'),
                'query' => request()->query(),
            ]);
            // Return to login with a user-friendly error message
            return redirect('/login')->withErrors('An unexpected error occurred during LinkedIn login. Please contact support.');
        }
    }
}
