<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;

class InvoiceEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $pdfPath;
    public $invoiceNumber;
    public $paymentDeadline;
    public $customerName;

    /**
     * Create a new message instance.
     */
    public function __construct($pdfPath, $invoiceNumber, $paymentDeadline, $customerName)
    {
        $this->pdfPath = $pdfPath;
        $this->invoiceNumber = $invoiceNumber;
        $this->paymentDeadline = $paymentDeadline;
        $this->customerName = $customerName;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Invoice ' . $this->invoiceNumber . ' - Business Eagles',
            from: '<EMAIL>',
            replyTo: '<EMAIL>'
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.invoice',
            with: [
                'customerName' => $this->customerName,
                'invoiceNumber' => $this->invoiceNumber,
                'paymentDeadline' => $this->paymentDeadline,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [
            Attachment::fromPath($this->pdfPath)
                ->as('Invoice-' . $this->invoiceNumber . '.pdf')
                ->withMime('application/pdf'),
        ];
    }
}
