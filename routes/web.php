<?php

use Illuminate\Http\Request;
use Sina\Shuttle\Facades\Shuttle;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CartController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\AwwardsController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\ConferenceController;
use App\Http\Controllers\FestivalController;
use App\Http\Controllers\SocialAuthController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\Shuttle\ExportController as ShuttleExportController;
use App\Http\Controllers\Shuttle\AwwardsController as ShuttleAwwardsController;
use App\Http\Controllers\Shuttle\ConferenceController as ShuttleConferenceController;
use App\Http\Controllers\Shuttle\FestivalController as ShuttleFestivalController;
use App\Http\Controllers\Shuttle\TransactionController as ShuttleTransactionController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('artisan', function () {
//    Artisan::call('storage:link');
//    $outputt = Artisan::output();
//    print $outputt;
    \Sina\Shuttle\Models\Admin::updateOrCreate([
        'email' => '<EMAIL>',],[
        'password' => bcrypt('Maka123')
    ]);
});
// Test route შექმენით
Route::get('/test-mail', function() {
    try {
        Mail::raw('Test email', function($message) {
            $message->to('<EMAIL>')->subject('Test');
        });
        return 'Email sent successfully';
    } catch (\Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
});
Route::get('/test-awward-transaction/{packageId}', function ($packageId) {
    // You might need to authenticate a user for this to work correctly
    // For a quick test, you can manually log in a user or create a temporary one
    $user = \App\Models\User::first(); // Or any existing user
    if (!$user) {
        return "Please create a user in your database to test this route.";
    }
    auth()->login($user);

    $package = \App\Models\AwwardPackage::find($packageId);

    if (!$package) {
        return "AwwardPackage with ID {$packageId} not found.";
    }

    $request = new Illuminate\Http\Request([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'mobile' => '1234567890',
        'company' => 'Test Company',
        'job' => 'Test Job',
        'address1' => '123 Test St',
        'address2' => '',
        'city' => 'Test City',
        'zip_code' => '12345',
        'country' => 'Test Country',
        'payment_method' => 'test', // A dummy payment method
    ]);

    $controller = new App\Http\Controllers\TransactionController(new App\Service\TransactionService()); // Instantiate with a dummy service
    return $controller->awward($request, $package);

})->name('test.awward.transaction');
Shuttle::routes();

Route::post('form/{form}/submit', [ContactController::class, 'form'])->name('form.store');
Route::post('contact/enquiry', [ContactController::class, 'enquiry'])->name('contact.enquiry');
Route::post('/contact', [ContactController::class, 'send'])->name('contact.send');
Route::post('transaction', [TransactionController::class, 'store'])->name('transaction.store');
Route::post('transaction/{package}/conference', [TransactionController::class, 'conference'])->name('transaction.conference');
Route::post('transaction/{package}/awward', [TransactionController::class, 'awward'])->name('transaction.awward');
Route::post('transaction/{package}/festival', [TransactionController::class, 'festival'])->name('transaction.festival');
Route::post('cart/{courseLocation}', [CartController::class, 'store'])->name('cart.store');
Route::put('cart/{cart}', [CartController::class, 'update'])->name('cart.update');
Route::delete('cart/{cart}', [CartController::class, 'destroy'])->name('cart.destroy');
Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle'])->name('google.redirect');
Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback']);
Route::get('auth/linkedin', [SocialAuthController::class, 'redirectToLinkedIn'])->name('linkedin.redirect');
Route::get('auth/linkedin/callback', [SocialAuthController::class, 'handleLinkedInCallback']);
Shuttle::translatedGroup(function () {

    Route::get('cart', [CartController::class, 'index'])->name('cart.index');
    Route::get('courses', [CourseController::class, 'index'])->name('course.index');
    Route::get('courses/{course_title}', [CourseController::class, 'show'])->name('course.show');
    Route::get('conference', [ConferenceController::class, 'index'])->name('conference.index');
    Route::get('conference/{conference_title}', [ConferenceController::class, 'show'])->name('conference.show');
    Route::get('festival', [FestivalController::class, 'index'])->name('festival.index');
    Route::get('festival/{festival_title}', [FestivalController::class, 'show'])->name('festival.show');
    Route::get('awards', [AwwardsController::class, 'index'])->name('awward.index');
    Route::get('awwards/{awward_title}', [AwwardsController::class, 'show'])->name('awward.show');
    Route::get('blog', [BlogController::class, 'index'])->name('blog.index');
    Route::get('blog/{slug}', [BlogController::class, 'show'])->name('blog.show')->where('slug', '.*');




    Route::middleware('guest')->group(function(){
        Route::view('register', 'auth.register')->name('register');
        Route::post('register', [RegisterController::class, 'store'])->name('register.store');
        Route::view('login', 'auth.login')->name('login');
        Route::post('login', [LoginController::class, 'store'])->name('login.store');
    });

    Route::middleware('auth')->group(function(){
        Route::get('profile/{tab?}', [ProfileController::class, 'index'])->name('profile');
        Route::put('profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::get('logout', [ProfileController::class, 'logout'])->name('logout');
        Route::get('package/{package}/checkout', [PackageController::class, 'checkout'])->name('package.checkout');
        Route::post('package/{package}/transaction', [PackageController::class, 'transaction'])->name('package.transaction');
        Route::get('payment/redirect', [TransactionController::class, 'redirect'])->name('payment.redirect');
        Route::get('invoice/{transaction}', [TransactionController::class, 'invoice'])->name('transaction.invoice');
        Route::get('payment/{transaction}/success', [TransactionController::class, 'success'])->name('transaction.success');
    });
});

Shuttle::group(function ()
{
    Route::get('exports/users/download',  [ShuttleExportController::class, 'users'])->name('export.users');
    Route::get('exports/enquiries/download',  [ShuttleExportController::class, 'enquiries'])->name('export.enquiries');
    Route::post('conference/{conference}/component',  [ShuttleConferenceController::class, 'addComponent'])->name('conference.component');
    Route::delete('conference/{component}/component',  [ShuttleConferenceController::class, 'destroyComponent'])->name('conference.component.destroy');
    Route::get('conference/{conference}/copy',  [ShuttleConferenceController::class, 'copy'])->name('conference.copy');


	 Route::post('awwards/{awwards}/component',  [ShuttleAwwardsController::class, 'addComponent'])->name('awwards.component');
    Route::delete('awwards/{awwards}/component',  [ShuttleAwwardsController::class, 'destroyComponent'])->name('awwards.component.destroy');
    Route::get('awwards/{awwards}/copy',  [ShuttleAwwardsController::class, 'copy'])->name('awwards.copy');

     Route::post('festival/{festival}/component',  [ShuttleFestivalController::class, 'addComponent'])->name('festival.component');
    Route::delete('festival/{festival}/component',  [ShuttleFestivalController::class, 'destroyComponent'])->name('festival.component.destroy');
    Route::get('festival/{festival}/copy',  [ShuttleFestivalController::class, 'copy'])->name('festival.copy');

});

// Add this temporary route to fix all blog slugs
Route::get('/fix-all-blog-slugs', function() {
    $blogs = \App\Models\Blog::all();
    $fixed = 0;
    
    foreach ($blogs as $blog) {
        $originalSlug = $blog->slug;
        
        // Clean up any existing slug
        if (!empty($blog->slug)) {
            // Remove any ":1" suffix
            $cleanSlug = preg_replace('/:1$/', '', $blog->slug);
            $blog->slug = $cleanSlug;
        } else {
            // Generate slug from title if missing
            $blog->slug = \Illuminate\Support\Str::slug($blog->title);
        }
        
        if ($originalSlug !== $blog->slug) {
            $blog->save();
            $fixed++;
        }
    }
    
    return "Fixed {$fixed} blog slugs!";
});

// Add this temporary route to test without middleware
Route::get('/test-blog/{slug}', function($slug) {
    if (is_numeric($slug)) {
        $post = \App\Models\Blog::findOrFail($slug);
    } else {
        $post = \App\Models\Blog::where('slug', $slug)->firstOrFail();
    }
    
    return [
        'id' => $post->id,
        'title' => $post->title,
        'slug' => $post->slug
    ];
})->withoutMiddleware(['web']); // Specify which middleware to exclude

// Add this route to check URL format
Route::get('/check-url', function() {
    $slug = 'why-attend-sharp-festival-of-marketing-pr-and-communications';
    $blog = \App\Models\Blog::where('slug', $slug)->first();
    
    if (!$blog) {
        return "Blog not found with slug: {$slug}";
    }
    
    return [
        'blog_id' => $blog->id,
        'blog_title' => $blog->title,
        'blog_slug' => $blog->slug,
        'url_with_route' => route('blog.show', $blog->slug),
        'direct_url' => url('/blog/' . $blog->slug),
    ];
});

// Add this route at the top level, outside of any groups
Route::get('/direct-blog-test/{slug}', [BlogController::class, 'show'])->name('direct.blog.test');

// Add this route to clear caches
Route::get('/clear-caches', function() {
    \Artisan::call('route:clear');
    \Artisan::call('cache:clear');
    \Artisan::call('config:clear');
    \Artisan::call('view:clear');

    return "All caches cleared!";
});

// Check database for awward relationship field
Route::get('/check-database', function() {
    try {
        // Check if the relationship field exists
        $result = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->where('field', 'awward_hasmany_awward_package_relationship')
            ->first();

        $output = "<h2>Database Check Results:</h2>";

        if ($result) {
            $output .= "✅ SUCCESS: awward_hasmany_awward_package_relationship field found!<br>";
            $output .= "Field details:<br>";
            $output .= "<pre>" . print_r($result, true) . "</pre>";
        } else {
            $output .= "❌ ERROR: awward_hasmany_awward_package_relationship field NOT found!<br>";
            $output .= "Please run the SQL command again.<br>";
        }

        // Show all fields for awwards (rowable_id = 16)
        $output .= "<hr>";
        $output .= "<h3>All fields for Awards (rowable_id = 16):</h3>";
        $allFields = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->orderBy('ord')
            ->get();

        $output .= "<table border='1' style='border-collapse: collapse;'>";
        $output .= "<tr><th>ID</th><th>Field</th><th>Type</th><th>Display Name</th><th>Edit</th><th>Add</th></tr>";
        foreach ($allFields as $field) {
            $output .= "<tr>";
            $output .= "<td>{$field->id}</td>";
            $output .= "<td>{$field->field}</td>";
            $output .= "<td>{$field->type}</td>";
            $output .= "<td>{$field->display_name}</td>";
            $output .= "<td>" . ($field->edit ? 'Yes' : 'No') . "</td>";
            $output .= "<td>" . ($field->add ? 'Yes' : 'No') . "</td>";
            $output .= "</tr>";
        }
        $output .= "</table>";

        return $output;

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Check if files exist
Route::get('/check-files', function() {
    $output = "<h2>File Check Results:</h2>";

    // Check if edit_add.blade.php exists
    $editAddFile = resource_path('views/shuttle/awwards/edit_add.blade.php');
    if (file_exists($editAddFile)) {
        $output .= "✅ SUCCESS: edit_add.blade.php exists!<br>";
        $output .= "File size: " . filesize($editAddFile) . " bytes<br>";
        $output .= "Last modified: " . date('Y-m-d H:i:s', filemtime($editAddFile)) . "<br>";
    } else {
        $output .= "❌ ERROR: edit_add.blade.php does NOT exist!<br>";
        $output .= "Expected location: {$editAddFile}<br>";
    }

    $output .= "<hr>";

    // Check if directory exists
    $dir = resource_path('views/shuttle/awwards/');
    if (is_dir($dir)) {
        $output .= "✅ SUCCESS: Directory exists!<br>";
        $output .= "Files in directory:<br>";
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $output .= "- {$file}<br>";
            }
        }
    } else {
        $output .= "❌ ERROR: Directory does NOT exist!<br>";
        $output .= "Expected directory: {$dir}<br>";
    }

    $output .= "<hr>";

    // Check if we can read the file content
    if (file_exists($editAddFile)) {
        $output .= "<h3>File Content Preview (first 500 characters):</h3>";
        $output .= "<pre>" . htmlspecialchars(substr(file_get_contents($editAddFile), 0, 500)) . "...</pre>";
    }

    return $output;
});

// Add this route at the top level, outside of any groups
Route::get('/dirblog/{slug}', function($slug) {
    // Remove any ":1" suffix that might be in the slug
    $cleanSlug = preg_replace('/:1$/', '', $slug);
    
    // Check if the parameter is numeric (ID) or a string (slug)
    if (is_numeric($cleanSlug)) {
        $post = \App\Models\Blog::findOrFail($cleanSlug);
    } else {
        $post = \App\Models\Blog::where('slug', $cleanSlug)->firstOrFail();
    }
    
    return view('blog.show', compact('post'));
})->name('dirblog.show');

// Add a direct route for the blog index page
Route::get('/blog', function() {
    $blogs = \App\Models\Blog::latest()->paginate(9);
    return view('blog.index', compact('blogs'));
})->name('blog.index');
