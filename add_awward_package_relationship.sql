-- Add awward_hasmany_awward_package_relationship field to shuttle_scaffold_interface_rows table

INSERT INTO `shuttle_scaffold_interface_rows` (`id`, `rowable_type`, `rowable_id`, `parent_id`, `field`, `type`, `display_name`, `required`, `browse`, `read`, `edit`, `add`, `delete`, `details`, `ord`, `last_upd`) VALUES
(NULL, 'Sina\\Shuttle\\Models\\ScaffoldInterface', 16, 0, 'awward_hasmany_awward_package_relationship', 'relationship', 'awward_packages', 0, 0, 1, 1, 1, 0, '{"key": "id", "type": "hasMany", "label": "title", "model": "App\\\\Models\\\\AwwardPackage", "pivot": null, "table": "awwards_packages", "column": "awward_id", "taggable": null, "pivot_table": null}', 18, 0);
