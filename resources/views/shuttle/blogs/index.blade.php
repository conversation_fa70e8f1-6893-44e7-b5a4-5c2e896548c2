@extends('shuttle::admin')

@section('breadcrumbs')
    @include('shuttle::includes.breadcrumbs', ['breadCrumbs' => ['Home' => route('shuttle.index'),
    $scaffoldInterface->display_name_plural => route('shuttle.scaffold_interface.index',$scaffoldInterface)], 'btn' =>
    ($add) ? route('shuttle.scaffold_interface.create',$scaffoldInterface) : null])
@stop

@push('css-vendors2')
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/dataTables.bootstrap4.min.css')}}" />
<link rel="stylesheet" href="{{route('shuttle.assets', 'css/vendor/datatables.responsive.bootstrap4.min.css')}}" />
@endpush

@section('main')

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search-input">Search Blogs:</label>
                            <input type="text" id="search-input" class="form-control" placeholder="Search by title, keyword, description, or content...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button id="search-btn" class="btn btn-primary btn-block">Search</button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button id="clear-btn" class="btn btn-secondary btn-block">Clear</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <table id="blogs-table" class="table table-striped table-bordered" style="width:100%">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Keyword</th>
                            <th>Description</th>
                            <th>Image</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@stop

@push('js')
<script src="{{route('shuttle.assets', 'js/vendor/jquery.dataTables.min.js')}}"></script>
<script src="{{route('shuttle.assets', 'js/vendor/dataTables.bootstrap4.min.js')}}"></script>
<script src="{{route('shuttle.assets', 'js/vendor/dataTables.responsive.min.js')}}"></script>
<script src="{{route('shuttle.assets', 'js/vendor/responsive.bootstrap4.min.js')}}"></script>

<script>
$(document).ready(function() {
    var table = $('#blogs-table').DataTable({
        processing: true,
        serverSide: true,
        searching: false, // Disable default search
        ajax: {
            url: "{{ route('shuttle.scaffold_interface.datatable', $scaffoldInterface) }}",
            type: 'GET',
            data: function(d) {
                d.search = {
                    value: $('#search-input').val(),
                    regex: false
                };
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'title', name: 'title' },
            { data: 'keyword', name: 'keyword' },
            { data: 'description', name: 'description' },
            { data: 'image', name: 'image', orderable: false, searchable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[5, 'desc']], // Order by created_at desc
        pageLength: 10,
        responsive: true,
        language: {
            processing: "Loading...",
            search: "Search:",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        }
    });

    // Custom search functionality
    $('#search-btn').on('click', function() {
        table.ajax.reload();
    });

    $('#search-input').on('keypress', function(e) {
        if (e.which == 13) { // Enter key
            table.ajax.reload();
        }
    });

    $('#clear-btn').on('click', function() {
        $('#search-input').val('');
        table.ajax.reload();
    });

    // Handle delete buttons
    $(document).on('click', '.remove-item', function(e) {
        e.preventDefault();
        var id = $(this).data('id');
        
        if (confirm('Are you sure you want to delete this blog?')) {
            var form = $('<form action="' + "{{route('shuttle.scaffold_interface.destroy',['scaffold_interface' => $scaffoldInterface, 'id' => '__id'])}}".replace('__id', id) + '" method="post">' +
                '@csrf' + '@method("DELETE")' + '</form>');
            form.appendTo('body');
            form.submit();
        }
    });
});
</script>
@endpush
