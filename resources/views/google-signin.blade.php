<!DOCTYPE html>
<html>
<head>
    <title>Google Sign-In Test</title>
    <meta name="google-signin-client_id" content="{{ config('services.google.client_id') }}">
    <script src="https://apis.google.com/js/platform.js" async defer></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <h1>Google Sign-In Test (Plesk-friendly)</h1>

    <div id="status"></div>

    <div style="background: #f0f0f0; padding: 10px; margin: 10px 0;">
        <strong>Debug Info:</strong><br>
        Google Client ID: {{ substr(config('services.google.client_id'), 0, 20) }}...<br>
        Current URL: {{ url()->current() }}<br>
        CSRF Token: {{ csrf_token() }}<br>
    </div>
    
    <!-- Google Sign-In Button -->
    <div class="g-signin2" data-onsuccess="onSignIn" data-theme="dark"></div>
    
    <br><br>
    <button onclick="signOut();">Sign Out</button>
    
    <script>
        function onSignIn(googleUser) {
            const profile = googleUser.getBasicProfile();
            const idToken = googleUser.getAuthResponse().id_token;
            
            document.getElementById('status').innerHTML = 'Signing in...';
            
            // Send token to server
            fetch('/auth/google/simple', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    token: idToken
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text(); // Get as text first to see what we're getting
            })
            .then(text => {
                console.log('Response text:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        document.getElementById('status').innerHTML = 'Success! Redirecting...';
                        window.location.href = data.redirect;
                    } else {
                        document.getElementById('status').innerHTML = 'Error: ' + (data.error || 'Unknown error');
                    }
                } catch (e) {
                    document.getElementById('status').innerHTML = 'Parse error: ' + e.message + '<br>Response: ' + text.substring(0, 500);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                document.getElementById('status').innerHTML = 'Network error: ' + error;
            });
        }
        
        function signOut() {
            const auth2 = gapi.auth2.getAuthInstance();
            auth2.signOut().then(function () {
                document.getElementById('status').innerHTML = 'Signed out.';
            });
        }
    </script>
</body>
</html>
