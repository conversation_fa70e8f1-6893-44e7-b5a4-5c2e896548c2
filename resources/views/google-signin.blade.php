<!DOCTYPE html>
<html>
<head>
    <title>Google Sign-In Test</title>
    <meta name="google-signin-client_id" content="{{ config('services.google.client_id') }}">
    <script src="https://apis.google.com/js/platform.js" async defer></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <h1>Google Sign-In Test (Plesk-friendly)</h1>
    
    <div id="status"></div>
    
    <!-- Google Sign-In Button -->
    <div class="g-signin2" data-onsuccess="onSignIn" data-theme="dark"></div>
    
    <br><br>
    <button onclick="signOut();">Sign Out</button>
    
    <script>
        function onSignIn(googleUser) {
            const profile = googleUser.getBasicProfile();
            const idToken = googleUser.getAuthResponse().id_token;
            
            document.getElementById('status').innerHTML = 'Signing in...';
            
            // Send token to server
            fetch('/auth/google/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    token: idToken
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('status').innerHTML = 'Success! Redirecting...';
                    window.location.href = data.redirect;
                } else {
                    document.getElementById('status').innerHTML = 'Error: ' + (data.error || 'Unknown error');
                }
            })
            .catch(error => {
                document.getElementById('status').innerHTML = 'Network error: ' + error;
            });
        }
        
        function signOut() {
            const auth2 = gapi.auth2.getAuthInstance();
            auth2.signOut().then(function () {
                document.getElementById('status').innerHTML = 'Signed out.';
            });
        }
    </script>
</body>
</html>
