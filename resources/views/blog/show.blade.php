@extends('app')

@section('content')
<section class="section pt-30px">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="section-title"><span>@lang('common.news_small_title')</span>
                    <h1>@lang('common.news_big_title')</h1>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="section news pt-0">
    <div class="container">
        <div class="row">
            {{-- Add a check to ensure $data is set and is iterable --}}
            @if(isset($data) && (is_array($data) || $data instanceof \Illuminate\Support\Collection) && count($data) > 0)
                @foreach ($data as $item_of_model)
                    <article class="pbmit-blog-style-1 col-md-4">
                        <div class="post-item">
                            <div class="pbmit-featured-container">
                                <div class="pbmit-featured-wrapper"><img
                                        src="{{ Storage::url(data_get($item_of_model, 'image')) }}" class="img-fluid"
                                        alt="{{ data_get($item_of_model, 'title') }}"></div>
                            </div>
                            <div class="pbminfotech-box-content">
                                <div class="pbmit-meta-date-wrapper"><span
                                        class="pbmit-day">{{ data_get($item_of_model, 'created_at')->locale(LaravelLocalization::getCurrentLocale())->isoFormat('D') }}</span><span>{{ data_get($item_of_model, 'created_at')->locale(LaravelLocalization::getCurrentLocale())->isoFormat('MMMM') }}</span>
                                </div>
                                <h3 class="pbmit-post-title"><a
                                        href="{{ $url }}/{{ data_get($item_of_model, 'slug') }}">{{ data_get($item_of_model, 'title') }}</a>
                                </h3>
                                <div class="pbminfotech-box-desc">
                                    <div class="pbmit-read-more-link"><a
                                            href="{{ $url }}/{{ data_get($item_of_model, 'slug') }}"><span>Read
                                                More</span></a></div>
                                </div>
                            </div>
                        </div>
                    </article>
                @endforeach
            @else
                <div class="col-12">
                    <p>No other news articles found.</p>
                </div>
            @endif
        </div>
    </div>
</section>

@endsection