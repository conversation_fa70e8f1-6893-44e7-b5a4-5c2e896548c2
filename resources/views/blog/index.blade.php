@extends('app')

@section('content')
<section class="section pt-30px">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="section-title">
                    <span>Blog</span>
                    <h1>Latest Articles</h1>
                </div>
            </div>
        </div>

        <!-- Search Form -->
        <div class="row mb-4">
            <div class="col-md-8 offset-md-2">
                <form method="GET" action="{{ route('blog.index') }}" class="search-form">
                    <div class="input-group">
                        <input type="text"
                               name="search"
                               class="form-control"
                               placeholder="Search articles by title..."
                               value="{{ request('search') }}"
                               style="border-radius: 25px 0 0 25px; padding: 15px 20px; border: 2px solid #e0e0e0;">
                        <div class="input-group-append">
                            <button class="btn btn-primary"
                                    type="submit"
                                    style="border-radius: 0 25px 25px 0; padding: 15px 25px; border: 2px solid #007bff;">
                                <i class="icofont-search"></i> Search
                            </button>
                        </div>
                    </div>
                    @if(request('search'))
                        <div class="mt-2">
                            <small class="text-muted">
                                Showing results for: "<strong>{{ request('search') }}</strong>"
                                <a href="{{ route('blog.index') }}" class="ml-2 text-primary">Clear search</a>
                            </small>
                        </div>
                    @endif
                </form>
            </div>
        </div>
    </div>
</section>
<section class="section news pt-0">
    <div class="container">
        <div class="row">
            @if($blogs->count() > 0)
                @foreach ($blogs as $blog)
                    <article class="pbmit-blog-style-1 col-md-4">
                        <div class="post-item">
                            <div class="pbmit-featured-container">
                                <div class="pbmit-featured-wrapper">
                                    <img src="{{ Storage::url($blog->image) }}" class="img-fluid" alt="{{ $blog->title }}">
                                </div>
                            </div>
                            <div class="pbminfotech-box-content">
                                <div class="pbmit-meta-date-wrapper">
                                    <span class="pbmit-day">{{ $blog->created_at->format('d') }}</span>
                                    <span>{{ $blog->created_at->format('F') }}</span>
                                </div>
                                <h3 class="pbmit-post-title">
                                    <a href="{{ route('dirblog.show', $blog->slug) }}">{{ $blog->title }}</a>
                                </h3>
                                <div class="pbminfotech-box-desc">
                                    <div class="pbmit-read-more-link">
                                        <a href="{{ route('dirblog.show', $blog->slug) }}"><span>Read More</span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                @endforeach
            @else
                <div class="col-md-12">
                    <div class="text-center py-5">
                        <div class="no-results">
                            <i class="icofont-search-document" style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                            <h3 class="text-muted">No articles found</h3>
                            @if(request('search'))
                                <p class="text-muted">No articles match your search for "<strong>{{ request('search') }}</strong>"</p>
                                <a href="{{ route('blog.index') }}" class="btn btn-primary">View All Articles</a>
                            @else
                                <p class="text-muted">There are no articles available at the moment.</p>
                            @endif
                        </div>
                    </div>
                </div>
            @endif
        </div>
        <div class="row">
            <div class="col-md-12">
                {{ $blogs->links() }}
            </div>
        </div>
    </div>
</section>
@endsection
