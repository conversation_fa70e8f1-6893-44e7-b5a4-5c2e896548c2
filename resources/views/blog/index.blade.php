@extends('app')

@section('content')
<section class="section pt-30px">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="section-title">
                    <span>Blog</span>
                    <h1>Latest Articles</h1>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="section news pt-0">
    <div class="container">
        <div class="row">
            @foreach ($blogs as $blog)
                <article class="pbmit-blog-style-1 col-md-4">
                    <div class="post-item">
                        <div class="pbmit-featured-container">
                            <div class="pbmit-featured-wrapper">
                                <img src="{{ Storage::url($blog->image) }}" class="img-fluid" alt="{{ $blog->title }}">
                            </div>
                        </div>
                        <div class="pbminfotech-box-content">
                            <div class="pbmit-meta-date-wrapper">
                                <span class="pbmit-day">{{ $blog->created_at->format('d') }}</span>
                                <span>{{ $blog->created_at->format('F') }}</span>
                            </div>
                            <h3 class="pbmit-post-title">
                                <a href="{{ route('dirblog.show', $blog->slug) }}">{{ $blog->title }}</a>
                            </h3>
                            <div class="pbminfotech-box-desc">
                                <div class="pbmit-read-more-link">
                                    <a href="{{ route('dirblog.show', $blog->slug) }}"><span>Read More</span></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </article>
            @endforeach
        </div>
        <div class="row">
            <div class="col-md-12">
                {{ $blogs->links() }}
            </div>
        </div>
    </div>
</section>
@endsection
