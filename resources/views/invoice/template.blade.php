<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice {{ $invoiceNumber }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .company-details {
            font-size: 14px;
            color: #666;
        }
        .invoice-details {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .invoice-left, .invoice-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .invoice-right {
            text-align: right;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .invoice-number {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .bill-to {
            margin-bottom: 30px;
        }
        .bill-to h3 {
            color: #007bff;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .items-table .amount {
            text-align: right;
        }
        .totals {
            float: right;
            width: 300px;
            margin-bottom: 30px;
        }
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        .totals td {
            padding: 8px 12px;
            border-bottom: 1px solid #ddd;
        }
        .totals .total-label {
            font-weight: bold;
            text-align: right;
        }
        .totals .total-amount {
            text-align: right;
            font-weight: bold;
        }
        .totals .grand-total {
            background-color: #007bff;
            color: white;
            font-size: 18px;
        }
        .payment-info {
            clear: both;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .payment-info h3 {
            color: #007bff;
            margin-top: 0;
        }
        .footer {
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .due-date {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">Business Eagles</div>
        <div class="company-details">
            Email: <EMAIL> | Phone: +44 ************<br>
            Website: www.business-eagles.com
        </div>
    </div>

    <div class="invoice-details">
        <div class="invoice-left">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number">{{ $invoiceNumber }}</div>
            <div>Date: {{ now()->format('d/m/Y') }}</div>
            <div class="due-date">Due Date: {{ $paymentDeadline }}</div>
        </div>
        <div class="invoice-right">
            <strong>Business Eagles Ltd</strong><br>
            London, United Kingdom<br>
            <EMAIL><br>
            +44 ************
        </div>
    </div>

    <div class="bill-to">
        <h3>Bill To:</h3>
        <strong>{{ $transaction->name }}</strong><br>
        {{ $transaction->company }}<br>
        {{ $transaction->address1 }}<br>
        @if($transaction->address2)
            {{ $transaction->address2 }}<br>
        @endif
        {{ $transaction->city }}, {{ $transaction->zip_code }}<br>
        {{ $transaction->country }}<br>
        Email: {{ $transaction->email }}<br>
        Phone: {{ $transaction->mobile }}
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th class="amount">Unit Price</th>
                <th class="amount">Total</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <strong>{{ $packageTitle }}</strong><br>
                    <small>{{ $transaction->job ?? 'Professional Package' }}</small>
                </td>
                <td>1</td>
                <td class="amount">£{{ number_format($transaction->price, 2) }}</td>
                <td class="amount">£{{ number_format($transaction->price, 2) }}</td>
            </tr>
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td class="total-label">Subtotal:</td>
                <td class="total-amount">£{{ number_format($transaction->price, 2) }}</td>
            </tr>
            <tr>
                <td class="total-label">VAT (20%):</td>
                <td class="total-amount">£0.00</td>
            </tr>
            <tr class="grand-total">
                <td class="total-label">Total:</td>
                <td class="total-amount">£{{ number_format($transaction->price, 2) }}</td>
            </tr>
        </table>
    </div>

    <div class="payment-info">
        <h3>Payment Information</h3>
        <p><strong>Payment Due Date:</strong> <span class="due-date">{{ $paymentDeadline }}</span></p>
        <p><strong>Payment Methods:</strong></p>
        <ul>
            <li>Bank Transfer: Please contact us for bank details</li>
            <li>Online Payment: Visit our website and use invoice number {{ $invoiceNumber }}</li>
            <li>Phone Payment: Call +44 ************</li>
        </ul>
        <p><strong>Reference:</strong> {{ $invoiceNumber }}</p>
    </div>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This invoice was generated automatically. For any questions, please contact <NAME_EMAIL></p>
    </div>
</body>
</html>
